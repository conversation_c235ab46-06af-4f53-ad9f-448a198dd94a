@echo off
echo ========================================
echo 微信支付测试环境快速配置脚本
echo ========================================
echo.

echo 1. 检查Java环境...
java -version
if %errorlevel% neq 0 (
    echo 错误：未找到Java环境，请先安装JDK
    pause
    exit /b 1
)

echo.
echo 2. 检查项目编译状态...
if not exist "xihuan-admin\target\xihuan-admin.jar" (
    echo 警告：未找到编译后的jar文件
    echo 请先编译项目：mvn clean package -DskipTests
    pause
)

echo.
echo 3. 启动内网穿透工具...
echo 请按以下步骤操作：
echo.
echo a) 下载ngrok: https://ngrok.com/download
echo b) 注册账号并获取authtoken
echo c) 在新的命令行窗口中运行: ngrok http 8080
echo d) 复制生成的https地址（如：https://abc123.ngrok.io）
echo.
set /p ngrok_url="请输入ngrok生成的https地址: "

echo.
echo 4. 更新配置文件...
echo 正在更新 application.yml 中的回调地址...

:: 创建临时配置文件
echo # 临时配置 - 微信支付回调地址 > temp_config.txt
echo wx: >> temp_config.txt
echo   pay: >> temp_config.txt
echo     notify-url: %ngrok_url%/miniapp/payment/notify/pay >> temp_config.txt

echo.
echo 5. 配置已更新，请手动修改以下文件：
echo 文件：xihuan-admin\src\main\resources\application.yml
echo 修改：notify-url: %ngrok_url%/miniapp/payment/notify/pay
echo.

echo 6. 启动后端服务...
echo 正在启动服务，请稍候...
cd xihuan-admin
start "XiHuan Backend" java -jar target\xihuan-admin.jar
cd ..

echo.
echo 7. 等待服务启动...
timeout /t 10 /nobreak

echo.
echo 8. 验证服务状态...
echo 正在检查服务是否启动成功...
curl -s http://localhost:8080/miniapp/payment/health > nul
if %errorlevel% equ 0 (
    echo ✅ 服务启动成功！
) else (
    echo ⚠️ 服务可能还在启动中，请稍后手动验证
)

echo.
echo ========================================
echo 配置完成！测试环境已准备就绪
echo ========================================
echo.
echo 📋 测试清单：
echo [ ] 1. ngrok已启动并获得公网地址
echo [ ] 2. application.yml中的notify-url已更新
echo [ ] 3. 后端服务已启动（端口8080）
echo [ ] 4. 小程序开发工具已配置服务器域名
echo.
echo 🔗 重要地址：
echo - 后端服务: http://localhost:8080
echo - 公网地址: %ngrok_url%
echo - 支付回调: %ngrok_url%/miniapp/payment/notify/pay
echo - 健康检查: %ngrok_url%/miniapp/payment/health
echo.
echo 📱 小程序测试步骤：
echo 1. 在小程序开发工具中配置服务器域名：%ngrok_url%
echo 2. 调用登录接口获取token
echo 3. 调用创建订单接口测试支付流程
echo 4. 使用微信开发者工具的支付调试功能
echo.
echo 🐛 调试命令：
echo - 查看日志: tail -f logs\xihuan-admin.log
echo - 测试订单: curl %ngrok_url%/miniapp/payment/create
echo - 检查回调: curl %ngrok_url%/miniapp/payment/notify/pay
echo.
echo 按任意键退出...
pause > nul
