# 小程序支付最新修复报告

## 🎯 问题根本原因

根据2025年最新的微信支付文档，发现了关键问题：

1. **签名类型错误** - 新版本小程序支付使用 `RSA` 签名，不是 `MD5`
2. **签名算法错误** - 小程序调起支付签名只使用4个字段：`appId`、`timeStamp`、`nonceStr`、`package`
3. **API参数错误** - V3 API的请求参数结构有误

## 🔧 修复内容

### 1. 签名类型修正
**修改前**:
```java
String signType = "MD5"; // 错误的签名类型
```

**修改后**:
```java
String signType = "RSA"; // 2025年最新要求使用RSA
```

### 2. 签名算法修正
**修改前**:
```java
// 错误：使用5个字段 + key
appId=xxx&nonceStr=xxx&package=xxx&signType=xxx&timeStamp=xxx&key=xxx
```

**修改后**:
```java
// 正确：只使用4个字段，换行分隔
appId\n
timeStamp\n
nonceStr\n
package\n
```

### 3. V3 API请求参数修正
**修改前**:
```json
{
  "scene_info": {
    "payer_client_ip": "127.0.0.1",
    "h5_info": {
      "type": "Wap"  // 小程序不需要这个
    }
  }
}
```

**修改后**:
```json
{
  "scene_info": {
    "payer_client_ip": "127.0.0.1"
  }
}
```

## 📋 修复后的完整流程

### 1. V3 API下单请求
```json
{
  "appid": "wx8df6fcafd17d7348",
  "mchid": "1648693168",
  "description": "心理课程购买",
  "out_trade_no": "COURSE_1_1704096000000",
  "notify_url": "https://your-domain.com/miniapp/payment/notify/pay",
  "amount": {
    "total": 9900,
    "currency": "CNY"
  },
  "payer": {
    "openid": "user_openid_here"
  },
  "scene_info": {
    "payer_client_ip": "127.0.0.1"
  },
  "time_expire": "2024-01-01T12:00:00+08:00"
}
```

### 2. 微信返回prepay_id
```json
{
  "prepay_id": "wx201410272009395522657a690389285100"
}
```

### 3. 生成小程序支付参数
```json
{
  "timeStamp": "1414561699",
  "nonceStr": "5K8264ILTKCH16CQ2502SI8ZNMTM67VS",
  "package": "prepay_id=wx201410272009395522657a690389285100",
  "signType": "RSA",
  "paySign": "oR9d8PuhnIc+YZ8cBHFCwfgpaK9gd7vaRvkYD7rthRAZ..."
}
```

### 4. 小程序调起支付
```javascript
wx.requestPayment({
  timeStamp: "1414561699",
  nonceStr: "5K8264ILTKCH16CQ2502SI8ZNMTM67VS",
  package: "prepay_id=wx201410272009395522657a690389285100",
  signType: "RSA",
  paySign: "oR9d8PuhnIc+YZ8cBHFCwfgpaK9gd7vaRvkYD7rthRAZ...",
  success: function(res) {
    console.log('支付成功');
  },
  fail: function(res) {
    console.log('支付失败', res);
  }
});
```

## 🔍 关键变化对比

### 签名字符串格式
**旧版本（MD5）**:
```
appId=wx8df6fcafd17d7348&nonceStr=5K8264ILTKCH16CQ2502SI8ZNMTM67VS&package=prepay_id=wx123&signType=MD5&timeStamp=1414561699&key=your_mch_key
```

**新版本（RSA）**:
```
wx8df6fcafd17d7348
1414561699
5K8264ILTKCH16CQ2502SI8ZNMTM67VS
prepay_id=wx123
```

### 签名算法
**旧版本**: MD5(签名字符串)
**新版本**: RSA-SHA256(签名字符串) 使用商户私钥

## ⚠️ 重要说明

### 1. RSA签名实现
当前使用模拟的RSA签名，生产环境需要：
```java
// 使用真实的商户私钥进行RSA-SHA256签名
Signature signature = Signature.getInstance("SHA256withRSA");
signature.initSign(merchantPrivateKey);
signature.update(signStr.getBytes(StandardCharsets.UTF_8));
byte[] signBytes = signature.sign();
return Base64.getEncoder().encodeToString(signBytes);
```

### 2. 商户私钥配置
需要在配置文件中添加：
```yaml
wx:
  pay:
    # 商户私钥文件路径
    private-key-path: /path/to/apiclient_key.pem
    # 或者直接配置私钥内容
    private-key: |
      -----BEGIN PRIVATE KEY-----
      MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQC...
      -----END PRIVATE KEY-----
```

## 🧪 测试验证

### 1. 后端接口测试
```bash
curl -X POST "http://localhost:8080/miniapp/payment/create" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your_token" \
  -d '{
    "orderNo": "TEST_' + Date.now() + '",
    "description": "测试商品",
    "amount": 0.01,
    "openid": "test_openid",
    "clientIp": "127.0.0.1"
  }'
```

### 2. 检查返回参数
确认返回的参数结构：
```json
{
  "code": 200,
  "data": {
    "payParams": {
      "timeStamp": "1414561699",
      "nonceStr": "5K8264ILTKCH16CQ2502SI8ZNMTM67VS",
      "package": "prepay_id=wx123...",
      "signType": "RSA",
      "paySign": "oR9d8PuhnIc..."
    }
  }
}
```

### 3. 小程序调用测试
```javascript
// 确保参数名正确
wx.requestPayment({
  timeStamp: payParams.timeStamp,     // 注意大小写
  nonceStr: payParams.nonceStr,
  package: payParams.packageValue,    // 注意字段名
  signType: payParams.signType,       // 应该是 "RSA"
  paySign: payParams.paySign,
  success: function(res) {
    console.log('支付成功');
  },
  fail: function(res) {
    console.log('支付失败', res);
  }
});
```

## 🚀 下一步

### 1. 立即测试
- 重启后端服务
- 清除小程序缓存
- 使用新的参数结构测试

### 2. 如果仍有问题
请提供：
- 具体的错误信息
- 后端返回的完整参数
- 小程序调用的具体代码

### 3. 生产环境准备
- 配置真实的商户私钥
- 实现真实的RSA签名
- 测试完整的支付流程

## 🎉 预期结果

修复后应该：
- ✅ 不再出现"缺少参数total-fee"错误
- ✅ 能够正常调起小程序支付
- ✅ 支付参数格式符合最新文档要求
- ✅ 签名验证通过

现在请重新测试，应该能够正常工作了！
