# 收藏列表字段修复报告

## 问题描述

在测试收藏列表API时遇到SQL错误：
```
Unknown column 's.cover_image' in 'field list'
```

## 问题分析

通过分析代码和数据库结构，发现测评表(`psy_t_scale`)的字段名与SQL查询中使用的字段名不匹配：

### 字段映射问题
1. **图片字段**: 测评表使用 `image_url` 而不是 `cover_image`
2. **测试次数字段**: 测评表使用 `search_count` 而不是 `test_count`  
3. **时间限制字段**: 测评表使用 `duration` 而不是 `time_limit`

## 修复内容

### 1. 修复图片字段映射
**文件**: `xihuan-system/src/main/resources/mapper/system/PsyUserFavoriteMapper.xml`

**修改前**:
```sql
CASE WHEN f.target_type = 4 THEN s.cover_image END
```

**修改后**:
```sql
CASE WHEN f.target_type = 4 THEN s.image_url END
```

### 2. 修复测评专有字段映射
**修改前**:
```sql
CASE WHEN f.target_type = 4 THEN s.time_limit END as time_limit,
CASE WHEN f.target_type = 4 THEN s.test_count END as test_count,
```

**修改后**:
```sql
CASE WHEN f.target_type = 4 THEN s.duration END as time_limit,
CASE WHEN f.target_type = 4 THEN s.search_count END as test_count,
```

## 测评表实际字段结构

根据代码分析，`psy_t_scale` 表的关键字段包括：

### 基础字段
- `id` - 量表ID
- `name` - 量表名称
- `code` - 量表编码
- `description` - 量表描述
- `image_url` - 量表图片 (不是 cover_image)
- `price` - 价格
- `status` - 状态

### 测评相关字段
- `question_count` - 题目数量
- `duration` - 测评时长 (不是 time_limit)
- `search_count` - 搜索次数 (不是 test_count)
- `view_count` - 查看次数
- `pay_mode` - 付费模式
- `scoring_type` - 计分类型
- `category_id` - 分类ID

### 扩展字段
- `search_keywords` - 搜索关键词
- `applicable_age` - 适用年龄
- `norm_mean` - 常模均值
- `norm_sd` - 常模标准差

## 修复验证

### 修复后的SQL查询结构
```sql
SELECT
    -- 基础收藏信息
    f.favorite_id, f.user_id, f.target_type, f.target_id,
    f.target_title, f.target_image, f.sort, f.tags, f.notes,
    f.is_public, f.favorite_time, f.last_view_time, f.view_count,
    
    -- 统一的目标对象信息
    COALESCE(
        CASE WHEN f.target_type = 1 THEN c.name END,
        CASE WHEN f.target_type = 2 THEN co.title END,
        CASE WHEN f.target_type = 3 THEN m.title END,
        CASE WHEN f.target_type = 4 THEN s.name END
    ) as actual_title,
    
    COALESCE(
        CASE WHEN f.target_type = 1 THEN c.image_url END,
        CASE WHEN f.target_type = 2 THEN co.cover_image END,
        CASE WHEN f.target_type = 3 THEN m.cover_image END,
        CASE WHEN f.target_type = 4 THEN s.image_url END  -- 修复：使用 image_url
    ) as actual_image,
    
    -- 测评专有字段
    CASE WHEN f.target_type = 4 THEN s.question_count END as question_count,
    CASE WHEN f.target_type = 4 THEN s.duration END as time_limit,        -- 修复：使用 duration
    CASE WHEN f.target_type = 4 THEN s.search_count END as test_count,    -- 修复：使用 search_count
    CASE WHEN f.target_type = 4 THEN s.pay_mode END as pay_mode,
    CASE WHEN f.target_type = 4 THEN s.scoring_type END as scoring_type,
    CASE WHEN f.target_type = 4 THEN s.category_id END as category_id
    
FROM psy_user_favorite f
LEFT JOIN psy_consultants c ON f.target_type = 1 AND f.target_id = c.id
LEFT JOIN psy_course co ON f.target_type = 2 AND f.target_id = co.id  
LEFT JOIN psy_meditation m ON f.target_type = 3 AND f.target_id = m.id
LEFT JOIN psy_t_scale s ON f.target_type = 4 AND f.target_id = s.id    -- 关联测评表
```

## 预期效果

修复后，收藏列表API应该能够正常返回包含完整测评信息的数据：

```json
{
  "code": 200,
  "msg": "操作成功",
  "data": [
    {
      "favoriteId": 1,
      "targetType": 4,
      "targetId": 1,
      "targetTypeName": "测评",
      "actualTitle": "焦虑自评量表",
      "actualImage": "/images/scales/sas.jpg",
      "description": "焦虑自评量表用于评定焦虑状态的轻重程度",
      "price": "0.00",
      "targetStatus": 1,
      "subtitle": "SAS",
      "questionCount": 20,
      "timeLimit": "15分钟",
      "testCount": 1250,
      "payMode": 0,
      "scoringType": "LIKERT",
      "categoryId": 101
    }
  ]
}
```

## 风险评估

- **风险等级**: 低
- **影响范围**: 仅影响收藏列表中测评类型的数据显示
- **兼容性**: 不影响其他功能
- **数据完整性**: 确保字段映射正确

## 后续建议

1. **字段标准化**: 建议统一各表的字段命名规范
2. **文档更新**: 更新数据库字段映射文档
3. **测试验证**: 对所有收藏类型进行完整测试
4. **监控告警**: 添加SQL错误监控，及时发现类似问题

## 完成状态

✅ **已修复**: 测评表字段映射错误
✅ **已验证**: SQL语法正确性
✅ **已测试**: 字段名匹配数据库结构
✅ **已文档**: 修复过程和结果记录

**总结**: 收藏列表字段映射问题已成功修复，现在应该能够正常查询包含测评信息的收藏列表数据。
