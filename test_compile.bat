@echo off
echo 测试编译微信支付相关代码...

echo 1. 编译 WxPayUtils...
javac -encoding UTF-8 -cp "xihuan-system/target/classes;xihuan-common/target/classes" xihuan-system/src/main/java/com/xihuan/system/utils/WxPayUtils.java
if %errorlevel% neq 0 (
    echo WxPayUtils 编译失败
    pause
    exit /b 1
)

echo 2. 编译 WxPayServiceImpl...
javac -encoding UTF-8 -cp "xihuan-system/target/classes;xihuan-common/target/classes" xihuan-system/src/main/java/com/xihuan/system/service/impl/WxPayServiceImpl.java
if %errorlevel% neq 0 (
    echo WxPayServiceImpl 编译失败
    pause
    exit /b 1
)

echo 3. 编译 MiniAppUnifiedPaymentController...
javac -encoding UTF-8 -cp "xihuan-admin/target/classes;xihuan-system/target/classes;xihuan-common/target/classes" xihuan-admin/src/main/java/com/xihuan/web/controller/miniapp/MiniAppUnifiedPaymentController.java
if %errorlevel% neq 0 (
    echo MiniAppUnifiedPaymentController 编译失败
    pause
    exit /b 1
)

echo ✅ 所有文件编译成功！
pause
