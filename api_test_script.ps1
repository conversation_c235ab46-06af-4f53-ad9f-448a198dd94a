# 测评系统API测试脚本
# 用于测试统一计分服务的API接口

param(
    [string]$BaseUrl = "http://localhost:8080",
    [string]$TestRecordId = "1"
)

Write-Host "🧪 测评系统API测试开始" -ForegroundColor Green
Write-Host "基础URL: $BaseUrl" -ForegroundColor Yellow
Write-Host "测试记录ID: $TestRecordId" -ForegroundColor Yellow
Write-Host ""

# 测试结果统计
$totalTests = 0
$passedTests = 0
$failedTests = 0

function Test-API {
    param(
        [string]$Name,
        [string]$Url,
        [string]$Method = "POST",
        [hashtable]$Headers = @{"Content-Type" = "application/json"},
        [string]$Body = $null
    )
    
    $global:totalTests++
    Write-Host "测试: $Name" -ForegroundColor Cyan
    Write-Host "URL: $Url" -ForegroundColor Gray
    
    try {
        if ($Method -eq "POST" -and $Body) {
            $response = Invoke-RestMethod -Uri $Url -Method $Method -Headers $Headers -Body $Body -TimeoutSec 30
        } else {
            $response = Invoke-RestMethod -Uri $Url -Method $Method -Headers $Headers -TimeoutSec 30
        }
        
        Write-Host "✅ 成功: $Name" -ForegroundColor Green
        Write-Host "响应: $($response | ConvertTo-Json -Depth 2)" -ForegroundColor Gray
        $global:passedTests++
        return $true
    }
    catch {
        Write-Host "❌ 失败: $Name" -ForegroundColor Red
        Write-Host "错误: $($_.Exception.Message)" -ForegroundColor Red
        $global:failedTests++
        return $false
    }
    finally {
        Write-Host ""
    }
}

# 测试用例定义
Write-Host "📋 开始API测试..." -ForegroundColor Green
Write-Host ""

# 1. 测试统一计分接口
Test-API -Name "统一计分服务" -Url "$BaseUrl/system/advanced-scoring/execute/$TestRecordId"

# 2. 测试PRCA-24计分
Test-API -Name "PRCA-24计分" -Url "$BaseUrl/system/advanced-scoring/prca24/$TestRecordId"

# 3. 测试STAI计分  
Test-API -Name "STAI计分" -Url "$BaseUrl/system/advanced-scoring/stai/$TestRecordId"

# 4. 测试SAS计分
Test-API -Name "SAS计分" -Url "$BaseUrl/system/advanced-scoring/sas/$TestRecordId"

# 5. 测试BAI计分
Test-API -Name "BAI计分" -Url "$BaseUrl/system/advanced-scoring/bai/$TestRecordId"

# 6. 测试反向计分工具
$reverseScoreBody = @{
    originalScore = 3
    maxValue = 5
} | ConvertTo-Json

Test-API -Name "反向计分工具" -Url "$BaseUrl/system/advanced-scoring/reverse-score" -Body $reverseScoreBody

# 7. 测试标准分转换工具
$standardScoreBody = @{
    rawScore = 25.5
    multiplier = 1.25
} | ConvertTo-Json

Test-API -Name "标准分转换工具" -Url "$BaseUrl/system/advanced-scoring/standard-score" -Body $standardScoreBody

# 8. 测试维度分数计算
Test-API -Name "维度分数计算" -Url "$BaseUrl/system/advanced-scoring/dimension-scores/$TestRecordId?scaleCode=PRCA-24"

# 9. 测试计分结果验证
$validateBody = @{
    scaleCode = "PRCA-24"
    scores = @{
        "GroupDiscussion" = 15.5
        "Meetings" = 18.0
        "InterpersonalConversation" = 12.5
        "PublicSpeaking" = 20.0
    }
} | ConvertTo-Json

Test-API -Name "计分结果验证" -Url "$BaseUrl/system/advanced-scoring/validate" -Body $validateBody

# 10. 测试计分报告生成
Test-API -Name "计分报告生成" -Url "$BaseUrl/system/advanced-scoring/report/$TestRecordId"

# 输出测试结果统计
Write-Host "📊 测试结果统计" -ForegroundColor Green
Write-Host "总测试数: $totalTests" -ForegroundColor White
Write-Host "通过: $passedTests" -ForegroundColor Green  
Write-Host "失败: $failedTests" -ForegroundColor Red

$successRate = if ($totalTests -gt 0) { [math]::Round(($passedTests / $totalTests) * 100, 2) } else { 0 }
Write-Host "成功率: $successRate%" -ForegroundColor $(if ($successRate -ge 80) { "Green" } elseif ($successRate -ge 60) { "Yellow" } else { "Red" })

if ($failedTests -eq 0) {
    Write-Host "🎉 所有测试通过！" -ForegroundColor Green
    exit 0
} elseif ($successRate -ge 80) {
    Write-Host "⚠️ 大部分测试通过，有少量问题需要关注" -ForegroundColor Yellow
    exit 1
} else {
    Write-Host "❌ 测试失败较多，需要检查系统状态" -ForegroundColor Red
    exit 2
}
