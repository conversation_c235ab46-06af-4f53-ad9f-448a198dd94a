# 测评系统功能测试计划

## 🎯 测试目标
验证P0和P1修复后的测评系统功能完整性，确保：
1. 系统能够正常编译和启动
2. 统一计分服务工作正常
3. 核心测评流程无问题
4. API接口响应正确

## 📋 测试范围

### 1. 编译和启动测试 🔴 高优先级
- [x] 项目编译测试
- [ ] 应用启动测试
- [ ] 依赖注入验证
- [ ] 数据库连接测试

### 2. 统一计分服务测试 🔴 高优先级
- [ ] 统一计分入口测试
- [ ] 配置化计分测试
- [ ] 硬编码计分测试
- [ ] 特定量表计分测试
- [ ] 工具方法测试

### 3. 核心业务流程测试 🟡 中优先级
- [ ] 测评开始流程
- [ ] 答题保存流程
- [ ] 测评完成流程
- [ ] 报告生成流程

### 4. API接口测试 🟡 中优先级
- [ ] 计分相关接口
- [ ] 测评记录接口
- [ ] 量表管理接口

### 5. 数据一致性测试 🟢 低优先级
- [ ] 数据库操作测试
- [ ] 事务完整性测试

## 🔧 测试环境准备

### 环境要求
- Java 8+
- MySQL 数据库
- Maven 构建工具
- 测试数据

### 测试数据准备
- 测试量表数据
- 测试题目和选项
- 测试用户和测评记录

## 📝 测试用例

### TC001: 编译测试
**目标**: 验证项目能够正常编译
**步骤**:
1. 清理项目：`mvn clean`
2. 编译项目：`mvn compile`
3. 打包项目：`mvn package -DskipTests`

**预期结果**: 编译成功，无错误信息

### TC002: 应用启动测试
**目标**: 验证应用能够正常启动
**步骤**:
1. 启动应用
2. 检查日志输出
3. 验证端口监听

**预期结果**: 应用正常启动，无异常日志

### TC003: 统一计分服务测试
**目标**: 验证新的统一计分服务功能
**测试方法**: 单元测试 + 集成测试

#### 子测试用例：
- TC003-1: executeScoring() 方法测试
- TC003-2: executeConfigurableScoring() 方法测试  
- TC003-3: executeAdvancedScoring() 方法测试
- TC003-4: 特定量表计分方法测试
- TC003-5: 工具方法测试

### TC004: API接口测试
**目标**: 验证关键API接口功能
**工具**: Postman/curl

#### 关键接口：
- POST /system/advanced-scoring/execute/{recordId}
- POST /system/advanced-scoring/prca24/{recordId}
- POST /system/advanced-scoring/stai/{recordId}
- POST /system/advanced-scoring/sas/{recordId}
- POST /system/advanced-scoring/bai/{recordId}

## 🚀 测试执行

### 阶段1: 基础验证测试
**时间**: 15分钟
**内容**: 编译、启动、基础功能

### 阶段2: 核心功能测试  
**时间**: 30分钟
**内容**: 计分服务、业务流程

### 阶段3: 集成测试
**时间**: 15分钟  
**内容**: API接口、端到端流程

## 📊 测试报告模板

### 测试结果记录
- ✅ 通过
- ❌ 失败  
- ⚠️ 部分通过
- 🔄 需要重测

### 问题记录格式
```
问题ID: P001
问题描述: [具体问题描述]
重现步骤: [详细步骤]
预期结果: [期望的结果]
实际结果: [实际发生的情况]
严重程度: [高/中/低]
状态: [待修复/已修复/已验证]
```

## 🎯 成功标准

### 必须通过的测试
- [x] 项目编译成功
- [ ] 应用正常启动
- [ ] 统一计分服务基本功能正常
- [ ] 至少3种量表计分功能正常

### 可接受的问题
- 非核心功能的小问题
- 性能问题（P2阶段解决）
- 界面显示问题

### 不可接受的问题
- 编译失败
- 应用启动失败
- 核心计分功能异常
- 数据丢失或损坏

让我们开始执行测试！
