# Java语法检查脚本
Write-Host "开始检查Java文件语法..." -ForegroundColor Green

# 检查关键的Java文件是否存在语法错误
$javaFiles = @(
    "xihuan-system\src\main\java\com\xihuan\system\service\IPsyTScoringService.java",
    "xihuan-system\src\main\java\com\xihuan\system\service\impl\PsyTScoringServiceImpl.java",
    "xihuan-system\src\main\java\com\xihuan\system\service\impl\PsyTSubscaleServiceImpl.java",
    "xihuan-admin\src\main\java\com\xihuan\web\controller\system\PsyTAdvancedScoringController.java",
    "xihuan-system\src\main\java\com\xihuan\system\service\impl\PsyTAssessmentRecordServiceImpl.java"
)

$errorCount = 0
$successCount = 0

foreach ($file in $javaFiles) {
    Write-Host "检查文件: $file" -ForegroundColor Yellow
    
    if (Test-Path $file) {
        # 检查基本语法问题
        $content = Get-Content $file -Raw
        
        # 检查常见语法错误
        $errors = @()
        
        # 检查括号匹配
        $openBraces = ($content -split '\{').Count - 1
        $closeBraces = ($content -split '\}').Count - 1
        if ($openBraces -ne $closeBraces) {
            $errors += "括号不匹配: { $openBraces 个, } $closeBraces 个"
        }
        
        # 检查分号
        $lines = $content -split "`n"
        for ($i = 0; $i -lt $lines.Count; $i++) {
            $line = $lines[$i].Trim()
            if ($line -match '^\s*(public|private|protected).*\{$' -or 
                $line -match '^\s*(if|for|while|switch).*\{$' -or
                $line -match '^\s*\}' -or
                $line -match '^\s*//.*$' -or
                $line -match '^\s*/\*.*\*/$' -or
                $line -match '^\s*@.*$' -or
                $line -match '^\s*(import|package).*$' -or
                $line -eq '') {
                continue
            }
            
            if ($line -match '\S' -and -not ($line -match ';$') -and -not ($line -match '\{$')) {
                # 可能缺少分号的行
                if ($line -match '(return|throw|break|continue)' -and -not ($line -match ';$')) {
                    $errors += "第 $($i+1) 行可能缺少分号: $line"
                }
            }
        }
        
        # 检查import语句
        if ($content -match 'import\s+[^;]*$') {
            $errors += "可能存在不完整的import语句"
        }
        
        if ($errors.Count -eq 0) {
            Write-Host "  ✅ 语法检查通过" -ForegroundColor Green
            $successCount++
        } else {
            Write-Host "  ❌ 发现语法问题:" -ForegroundColor Red
            foreach ($error in $errors) {
                Write-Host "    - $error" -ForegroundColor Red
            }
            $errorCount++
        }
    } else {
        Write-Host "  ❌ 文件不存在" -ForegroundColor Red
        $errorCount++
    }
    
    Write-Host ""
}

Write-Host "检查完成!" -ForegroundColor Green
Write-Host "成功: $successCount 个文件" -ForegroundColor Green
Write-Host "错误: $errorCount 个文件" -ForegroundColor Red

if ($errorCount -eq 0) {
    Write-Host "🎉 所有检查的文件语法正确!" -ForegroundColor Green
    exit 0
} else {
    Write-Host "⚠️ 发现语法问题，需要修复" -ForegroundColor Yellow
    exit 1
}
